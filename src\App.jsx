import StoryForm from './components/StoryForm'
import CommunityStats from './components/CommunityStats'
import ScrollToTop, { showToast } from './components/ScrollToTop'
import SearchAndFilters from './components/SearchAndFilters'
import StoryOfTheMonth from './components/StoryOfTheMonth'
import ConfettiCelebration from './components/ConfettiCelebration'
import ProgressTracker from './components/ProgressTracker'
import EconomicEmpowerment from './components/EconomicEmpowerment'
import CommunityDialogue from './components/CommunityDialogue'
import CommunitySupport from './components/CommunitySupport'
import CommunityActivism from './components/CommunityActivism'
import TaskManagementDashboard from './components/TaskManagementDashboard'
import { useState, useEffect } from 'react'
import './App.css'
import './auth.css'
import { useAuth } from './AuthContext'
import Signup from './components/Signup'
import Login from './components/Login'
import PasswordReset from './components/PasswordReset'
import { Link } from 'react-router-dom'

const FEATURED_QUOTES = [
  '"If you want to go fast, go alone. If you want to go far, go together." – African Proverb',
  '"The time is always right to do what is right." – <PERSON>.',
  '"Success is to be measured not so much by the position that one has reached in life as by the obstacles which he has overcome." – Booker T. Washington',
  '"Never be limited by other people\'s limited imagination." – Mae Jemison',
  '"If you don\'t like something, change it. If you can\'t change it, change your attitude." – Maya Angelou'
];

const RESOURCES = [
  { name: 'NAACP', url: 'https://naacp.org/' },
  { name: 'Black Girls Code', url: 'https://www.blackgirlscode.com/' },
  { name: 'National Urban League', url: 'https://nul.org/' },
  { name: 'The Conscious Kid', url: 'https://www.theconsciouskid.org/' },
];

const TOPICS = [
  'All Stories',
  'Culture & Heritage',
  'Family & Community',
  'Art & Creativity',
  'History & Legacy',
  'Education & Achievement',
  'Business & Entrepreneurship',
  'Health & Wellness',
  'Inspiration & Overcoming',
  'Activism & Leadership',
  'Joy & Celebration',
];

function App() {
  const [stories, setStories] = useState([])
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedTopic, setSelectedTopic] = useState('All Stories')
  const [featuredStory, setFeaturedStory] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [bookmarkedStories, setBookmarkedStories] = useState([])
  const [showBookmarksOnly, setShowBookmarksOnly] = useState(false)
  const [storyOfTheMonth, setStoryOfTheMonth] = useState(null)
  const [votingPeriod, setVotingPeriod] = useState(new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }))
  const [showCelebration, setShowCelebration] = useState(false)
  const [darkMode, setDarkMode] = useState(false)
  const [userProfiles, setUserProfiles] = useState({})
  const { currentUser: authUser, logout, fetchUserProfile } = useAuth();
  const [userProfile, setUserProfile] = useState(null);
  const [showLogin, setShowLogin] = useState(true);
  const [showReset, setShowReset] = useState(false);
  const [currentView, setCurrentView] = useState('stories'); // 'stories', 'economic', 'dialogue', 'support', 'activism'
  const [showTaskManagement, setShowTaskManagement] = useState(false);

  useEffect(() => {
    async function loadProfile() {
      if (authUser) {
        const profile = await fetchUserProfile(authUser.uid);
        setUserProfile(profile);
      }
    }
    loadProfile();
  }, [authUser, fetchUserProfile])



  // Rotate quotes every 8 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuoteIndex((prev) => (prev + 1) % FEATURED_QUOTES.length)
    }, 8000)
    return () => clearInterval(interval)
  }, [])

  // Update featured story when stories change
  useEffect(() => {
    if (stories.length > 0) {
      // Find story with most reactions or latest story
      const mostLiked = stories.reduce((prev, current) =>
        (prev.hearts + prev.claps) > (current.hearts + current.claps) ? prev : current
      )
      setFeaturedStory(mostLiked)
    } else {
      setFeaturedStory(null)
    }
  }, [stories])

  // Update Story of the Month
  useEffect(() => {
    if (stories.length > 0) {
      const previousWinner = storyOfTheMonth;
      const monthlyCandidate = stories
        .filter(story => story.monthlyVotes > 0)
        .sort((a, b) => b.monthlyVotes - a.monthlyVotes)[0]
      
      if (monthlyCandidate && (!previousWinner || monthlyCandidate.id !== previousWinner.id)) {
        setStoryOfTheMonth(monthlyCandidate)
        if (previousWinner) {
          setShowCelebration(true)
        }
      }
    }
  }, [stories, storyOfTheMonth])

  async function handleStorySubmit(story) {
    setIsSubmitting(true)

    const newStory = {
      ...story,
      id: Date.now(),
      imageUrl: story.image ? URL.createObjectURL(story.image) : null,
      timestamp: new Date().toLocaleDateString(),
      hearts: 0,
      claps: 0,
      shares: 0,
      monthlyVotes: 0,
      readingTime: Math.ceil(story.content.split(' ').length / 200), // ~200 words per minute
      author: authUser?.uid || 'anonymous',
      tags: story.tags || [],
      isDraft: false
    }

    setStories(prevStories => [newStory, ...prevStories])
    setIsSubmitting(false)

    // Update user profile stats
    if (authUser) {
      setUserProfiles(prev => ({
        ...prev,
        [authUser.uid]: {
          ...prev[authUser.uid],
          totalStories: (prev[authUser.uid]?.totalStories || 0) + 1
        }
      }))
    }

    showToast('🎉 Your story has been shared with the community!', 'success')
  }



  function handleReaction(storyId, reactionType) {
    setStories(stories.map(story => 
      story.id === storyId 
        ? { ...story, [reactionType]: story[reactionType] + 1 }
        : story
    ))
    
    // Show encouraging toast
    const messages = {
      hearts: ['❤️ Spreading love!', '💕 Love shared!', '❤️ Beautiful!'],
      claps: ['👏 Amazing support!', '🎉 Well applauded!', '👏 Fantastic!']
    }
    const randomMessage = messages[reactionType][Math.floor(Math.random() * messages[reactionType].length)]
    showToast(randomMessage, 'success')
  }

  function handleShare(story) {
    // Increment share count
    setStories(stories.map(s => 
      s.id === story.id 
        ? { ...s, shares: s.shares + 1 }
        : s
    ))
    
    // Copy story to clipboard for sharing
    const shareText = `Check out this inspiring story from NAROOP: "${story.title}" - ${window.location.href}`
    navigator.clipboard.writeText(shareText)
    showToast('📋 Story link copied to clipboard! Share the inspiration! ✨', 'info')
  }

  function handleBookmark(storyId) {
    if (bookmarkedStories.includes(storyId)) {
      setBookmarkedStories(bookmarkedStories.filter(id => id !== storyId))
      showToast('📖 Bookmark removed', 'info')
    } else {
      setBookmarkedStories([...bookmarkedStories, storyId])
      showToast('📚 Story bookmarked! Check your saved stories.', 'success')
    }
  }

  function handleMonthlyVote(storyId) {
    setStories(stories.map(story => 
      story.id === storyId 
        ? { ...story, monthlyVotes: story.monthlyVotes + 1 }
        : story
    ))
    showToast('🗳️ Vote cast for Story of the Month!', 'success')
  }

  function handleSearch(query) {
    setSearchQuery(query)
  }

  function toggleDarkMode() {
    setDarkMode(!darkMode)
    document.body.classList.toggle('dark-mode', !darkMode)
  }

  function exportStory(story) {
    const storyText = `
${story.title}
By: ${userProfiles[story.author]?.name || 'Community Member'}
Topic: ${story.topic}
Date: ${story.timestamp}
Tags: ${story.tags?.join(', ') || 'None'}

${story.content}

---
Shared on NAROOP - Narrative of Our People
${window.location.href}
    `.trim()

    const blob = new Blob([storyText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    showToast('📄 Story exported successfully!', 'success')
  }

  function printStory(story) {
    const printWindow = window.open('', '_blank')
    printWindow.document.write(`
      <html>
        <head>
          <title>${story.title}</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; line-height: 1.6; }
            h1 { color: #e63946; border-bottom: 2px solid #fbbf24; padding-bottom: 10px; }
            .meta { color: #666; margin-bottom: 20px; }
            .tags { margin-top: 20px; }
            .tag { background: #fbbf24; color: #222; padding: 2px 8px; border-radius: 12px; margin-right: 5px; font-size: 0.8em; }
          </style>
        </head>
        <body>
          <h1>${story.title}</h1>
          <div class="meta">
            <p><strong>By:</strong> ${userProfiles[story.author]?.name || 'Community Member'}</p>
            <p><strong>Topic:</strong> ${story.topic}</p>
            <p><strong>Date:</strong> ${story.timestamp}</p>
            <p><strong>Reading Time:</strong> ${story.readingTime} min</p>
          </div>
          <div class="content">
            ${story.content.split('\n').map(p => `<p>${p}</p>`).join('')}
          </div>
          ${story.tags?.length ? `
            <div class="tags">
              <strong>Tags:</strong> ${story.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
          ` : ''}
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; text-align: center; color: #666;">
            <p>Shared on NAROOP - Narrative of Our People</p>
          </div>
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }

  // Advanced filtering and search
  const getFilteredAndSortedStories = () => {
    let filtered = stories;

    // Filter by topic
    if (selectedTopic !== 'All Stories') {
      filtered = filtered.filter(story => story.topic === selectedTopic)
    }

    // Filter by bookmarks if enabled
    if (showBookmarksOnly) {
      filtered = filtered.filter(story => bookmarkedStories.includes(story.id))
    }

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(story =>
        story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        story.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (story.tags && story.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
      )
    }

    // Sort
    switch (sortBy) {
      case 'newest':
        return filtered.sort((a, b) => b.id - a.id)
      case 'oldest':
        return filtered.sort((a, b) => a.id - b.id)
      case 'mostLoved':
        return filtered.sort((a, b) => (b.hearts + b.claps) - (a.hearts + a.claps))
      case 'mostShared':
        return filtered.sort((a, b) => b.shares - a.shares)
      case 'monthlyVotes':
        return filtered.sort((a, b) => b.monthlyVotes - a.monthlyVotes)
      default:
        return filtered
    }
  }

  const filteredStories = getFilteredAndSortedStories()



  if (!authUser) {
    if (showReset) {
      return <PasswordReset onBackToLogin={() => setShowReset(false)} />;
    }
    return showLogin ? (
      <Login 
        onSwitchToSignup={() => setShowLogin(false)} 
        onSwitchToReset={() => setShowReset(true)}
      />
    ) : (
      <Signup onSwitchToLogin={() => setShowLogin(true)} />
    );
  }

  return (
    <main className={`naroop-main ${darkMode ? 'dark-mode' : ''}`}>
      <header className="naroop-auth-header">
        <span>Welcome, {userProfile?.name || authUser.email}</span>
        <Link to="/account" className="naroop-account-link" aria-label="Go to your account">My Account</Link>
        <button onClick={logout} className="naroop-logout-btn">Log Out</button>
      </header>

      <nav className="naroop-main-nav">
        <button
          className={`nav-btn ${currentView === 'stories' ? 'active' : ''}`}
          onClick={() => setCurrentView('stories')}
        >
          📖 Stories
        </button>
        <button
          className={`nav-btn ${currentView === 'economic' ? 'active' : ''}`}
          onClick={() => setCurrentView('economic')}
        >
          💰 Economic Hub
        </button>
        <button
          className={`nav-btn ${currentView === 'dialogue' ? 'active' : ''}`}
          onClick={() => setCurrentView('dialogue')}
        >
          🗣️ Dialogue
        </button>
        <button
          className={`nav-btn ${currentView === 'support' ? 'active' : ''}`}
          onClick={() => setCurrentView('support')}
        >
          🤝 Support
        </button>
        <button
          className={`nav-btn ${currentView === 'activism' ? 'active' : ''}`}
          onClick={() => setCurrentView('activism')}
        >
          ✊🏾 Activism
        </button>
        <Link to="/kids" className="nav-btn kids-nav-btn">
          🌟 Kids Zone
        </Link>
        <button
          className="nav-btn task-management-btn"
          onClick={() => setShowTaskManagement(true)}
          title="Manage your content and tasks"
        >
          🧹 Manage
        </button>
      </nav>
      
      <section className="naroop-hero">
        <div className="naroop-hero-header">
          <div className="naroop-hero-content">
            <h1>NAROOP</h1>
            <h2>Narrative of Our People</h2>
            <p className="naroop-mission">
              A positive space for Black voices to be heard through stories, experiences, and encouragement. Our mission is to uplift, inspire, and shine a bright light on the Black community—celebrating greatness, unity, and growth.
            </p>
          </div>
          <button 
            className="naroop-dark-mode-toggle"
            onClick={toggleDarkMode}
            title={darkMode ? "Switch to light mode" : "Switch to dark mode"}
          >
            {darkMode ? '☀️' : '🌙'}
          </button>
        </div>
      </section>
      
      <div className="naroop-featured-quote" key={currentQuoteIndex}>
        {FEATURED_QUOTES[currentQuoteIndex]}
      </div>

      {/* Render different views based on currentView state */}
      {currentView === 'stories' && (
        <>
          {/* Featured Story Section */}
          {featuredStory && (
            <section className="naroop-featured-story">
              <div className="naroop-featured-container">
                <h3>⭐ Featured Community Story</h3>
                <div className="naroop-featured-content">
                  <div className="naroop-featured-info">
                    <span className="naroop-story-topic">{featuredStory.topic}</span>
                    <h4>{featuredStory.title}</h4>
                    <p>{featuredStory.content.substring(0, 150)}...</p>
                    <div className="naroop-story-reactions">
                      <span className="naroop-reaction-display">
                        ❤️ {featuredStory.hearts} 👏 {featuredStory.claps} 📤 {featuredStory.shares}
                      </span>
                    </div>
                  </div>
                  {featuredStory.imageUrl && (
                    <div className="naroop-featured-image">
                      <img src={featuredStory.imageUrl} alt="Featured story" />
                    </div>
                  )}
                </div>
              </div>
            </section>
          )}

          <StoryOfTheMonth
            story={storyOfTheMonth}
            votingPeriod={votingPeriod}
          />
        </>
      )}

      {currentView === 'economic' && <EconomicEmpowerment />}
      {currentView === 'dialogue' && <CommunityDialogue />}
      {currentView === 'support' && <CommunitySupport />}
      {currentView === 'activism' && <CommunityActivism />}
      
      {currentView === 'stories' && (
        <div className="naroop-content">
        <div className="naroop-form-col">
          <StoryForm onSubmit={handleStorySubmit} isSubmitting={isSubmitting} />
          <ProgressTracker
            stories={stories}
            currentUser={authUser?.uid || 'anonymous'}
            userProfiles={userProfiles}
          />
        </div>
        <section className="naroop-stories">
          <SearchAndFilters
            searchQuery={searchQuery}
            onSearchChange={handleSearch}
            selectedTopic={selectedTopic}
            onTopicChange={setSelectedTopic}
            sortBy={sortBy}
            onSortChange={setSortBy}
            showBookmarksOnly={showBookmarksOnly}
            onBookmarksToggle={() => setShowBookmarksOnly(!showBookmarksOnly)}
            bookmarkCount={bookmarkedStories.length}
            TOPICS={TOPICS}
            stories={stories}
          />

          <div className="naroop-stories-header">
            <h3>
              Community Stories ({filteredStories.length})
              {searchQuery && ` • "${searchQuery}"`}
              {showBookmarksOnly && ' • Bookmarked'}
            </h3>
          </div>

          <div className="naroop-story-list">
            {filteredStories.length === 0 ? (
              <div className="naroop-story-placeholder">
                {searchQuery ? (
                  <p>🔍 No stories found for "{searchQuery}". Try different keywords or browse all stories!</p>
                ) : showBookmarksOnly ? (
                  <p>📚 No bookmarked stories yet. Bookmark your favorite stories to see them here!</p>
                ) : selectedTopic === 'All Stories' ? (
                  <p>✨ Stories from our community will appear here. Share your journey, inspire others, and help us grow together! ✨</p>
                ) : (
                  <p>🔍 No stories found for "{selectedTopic}". Be the first to share a story in this category!</p>
                )}
              </div>
            ) : (
              filteredStories.map(story => (
                <article key={story.id} className="naroop-story">
                  <div className="naroop-story-meta">
                    <span className="naroop-story-topic">{story.topic}</span>
                    <span className="naroop-story-date">{story.timestamp}</span>
                    <span className="naroop-story-author">by {userProfiles[story.author]?.name || 'Community Member'}</span>
                  </div>
                  <h4>{story.title}</h4>
                  <div className="naroop-story-meta-reading">
                    <span className="naroop-reading-time">⏱️ {story.readingTime} min read</span>
                  </div>
                  {story.tags && story.tags.length > 0 && (
                    <div className="naroop-story-tags">
                      {story.tags.map(tag => (
                        <span key={tag} className="naroop-tag">#{tag}</span>
                      ))}
                    </div>
                  )}
                  <p>{story.content}</p>
                  {story.imageUrl && <img src={story.imageUrl} alt="Story visual" className="naroop-story-img" />}
                  <div className="naroop-story-actions">
                    <button 
                      className="naroop-reaction-btn naroop-heart-btn"
                      onClick={() => handleReaction(story.id, 'hearts')}
                      title="Show love"
                    >
                      ❤️ {story.hearts}
                    </button>
                    <button 
                      className="naroop-reaction-btn naroop-clap-btn"
                      onClick={() => handleReaction(story.id, 'claps')}
                      title="Applaud this story"
                    >
                      👏 {story.claps}
                    </button>
                    <button 
                      className="naroop-reaction-btn naroop-share-btn"
                      onClick={() => handleShare(story)}
                      title="Share this story"
                    >
                      📤 {story.shares}
                    </button>
                    <button 
                      className={`naroop-reaction-btn naroop-bookmark-btn ${bookmarkedStories.includes(story.id) ? 'bookmarked' : ''}`}
                      onClick={() => handleBookmark(story.id)}
                      title={bookmarkedStories.includes(story.id) ? "Remove bookmark" : "Bookmark this story"}
                    >
                      {bookmarkedStories.includes(story.id) ? '🔖' : '📚'}
                    </button>
                    {votingPeriod === 'June 2025' && (
                      <button 
                        className="naroop-reaction-btn naroop-vote-btn"
                        onClick={() => handleMonthlyVote(story.id)}
                        title="Vote for Story of the Month"
                      >
                        🗳️ {story.monthlyVotes}
                      </button>
                    )}
                    <button 
                      className="naroop-reaction-btn naroop-export-btn"
                      onClick={() => exportStory(story)}
                      title="Export story"
                    >
                      📄
                    </button>
                    <button 
                      className="naroop-reaction-btn naroop-print-btn"
                      onClick={() => printStory(story)}
                      title="Print story"
                    >
                      🖨️
                    </button>
                  </div>
                </article>
              ))
            )}
          </div>
        </section>
        </div>
      )}

      {/* Common sections for all views */}
      <CommunityStats stories={stories} />

      <section className="naroop-resources">
        <h3>🤝 Resources & Support</h3>
        <ul>
          {RESOURCES.map(r => (
            <li key={r.url}>
              <a href={r.url} target="_blank" rel="noopener noreferrer">{r.name}</a>
            </li>
          ))}
        </ul>
      </section>

      <section className="naroop-about">
        <h3>About NAROOP</h3>
        <p>
          NAROOP (Narrative of Our People) is dedicated to uplifting Black voices and sharing positive stories, experiences, and resources. We believe in the power of community, unity, and storytelling to inspire and create change.
        </p>
      </section>
      
      <ConfettiCelebration
        trigger={showCelebration}
        message="🏆 New Story of the Month! 🎉"
      />

      {showTaskManagement && (
        <TaskManagementDashboard
          onClose={() => setShowTaskManagement(false)}
        />
      )}

      <ScrollToTop />
    </main>
  )
}

export default App
