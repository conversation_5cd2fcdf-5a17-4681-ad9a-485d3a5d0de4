import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../AuthContext';
import { collection, addDoc, query, where, orderBy, onSnapshot, doc, updateDoc, arrayUnion } from 'firebase/firestore';
import { db } from '../firebase';

const SUPPORT_CATEGORIES = [
  {
    id: 'safety_security',
    title: 'Safety & Security',
    description: 'Community safety, crime prevention, and security resources',
    icon: '🛡️',
    color: '#FF6B6B',
    resources: [
      { name: 'National Crime Prevention Council', url: 'https://www.ncpc.org/' },
      { name: 'Community Safety Toolkit', url: 'https://www.communitysafetytoolkit.org/' },
      { name: 'Neighborhood Watch Programs', url: 'https://www.nnw.org/' },
      { name: 'Crisis Text Line', url: 'https://www.crisistextline.org/', urgent: true }
    ]
  },
  {
    id: 'economic_assistance',
    title: 'Economic Assistance',
    description: 'Financial aid, job opportunities, and economic support',
    icon: '💼',
    color: '#4ECDC4',
    resources: [
      { name: 'SNAP Benefits', url: 'https://www.fns.usda.gov/snap' },
      { name: 'Unemployment Benefits', url: 'https://www.dol.gov/general/topic/unemployment-insurance' },
      { name: 'Housing Assistance', url: 'https://www.hud.gov/topics/rental_assistance' },
      { name: '211 - Community Resources', url: 'https://www.211.org/', urgent: true }
    ]
  },
  {
    id: 'mental_health',
    title: 'Mental Health & Wellness',
    description: 'Mental health support, counseling, and wellness resources',
    icon: '🧠',
    color: '#45B7D1',
    resources: [
      { name: 'Black Mental Health Alliance', url: 'https://blackmentalhealth.com/' },
      { name: 'National Suicide Prevention Lifeline', url: 'https://suicidepreventionlifeline.org/', urgent: true },
      { name: 'Therapy for Black Girls', url: 'https://therapyforblackgirls.com/' },
      { name: 'Mental Health America', url: 'https://www.mhanational.org/' }
    ]
  },
  {
    id: 'legal_aid',
    title: 'Legal Aid & Justice',
    description: 'Legal assistance, rights information, and justice resources',
    icon: '⚖️',
    color: '#96CEB4',
    resources: [
      { name: 'NAACP Legal Defense Fund', url: 'https://www.naacpldf.org/' },
      { name: 'Legal Aid Society', url: 'https://www.legalaid.org/' },
      { name: 'Know Your Rights', url: 'https://www.aclu.org/know-your-rights' },
      { name: 'National Bar Association', url: 'https://www.nationalbar.org/' }
    ]
  },
  {
    id: 'education_youth',
    title: 'Education & Youth',
    description: 'Educational support, youth programs, and development resources',
    icon: '📚',
    color: '#FFEAA7',
    resources: [
      { name: 'Boys & Girls Clubs', url: 'https://www.bgca.org/' },
      { name: 'United Negro College Fund', url: 'https://uncf.org/' },
      { name: 'After School Programs', url: 'https://www.afterschoolalliance.org/' },
      { name: 'Mentoring Programs', url: 'https://www.mentoring.org/' }
    ]
  },
  {
    id: 'healthcare',
    title: 'Healthcare Access',
    description: 'Healthcare services, insurance, and medical assistance',
    icon: '🏥',
    color: '#DDA0DD',
    resources: [
      { name: 'Community Health Centers', url: 'https://findahealthcenter.hrsa.gov/' },
      { name: 'Medicaid Information', url: 'https://www.medicaid.gov/' },
      { name: 'Black Doctors Network', url: 'https://blackdoctor.org/' },
      { name: 'Free Clinics', url: 'https://www.freeclinics.com/' }
    ]
  }
];

const SUPPORT_REQUEST_TYPES = [
  { id: 'immediate', label: 'Immediate Help Needed', priority: 'urgent', color: '#FF4757' },
  { id: 'guidance', label: 'Seeking Guidance', priority: 'normal', color: '#3742FA' },
  { id: 'resources', label: 'Looking for Resources', priority: 'normal', color: '#2ED573' },
  { id: 'offering', label: 'Offering Help', priority: 'normal', color: '#FFA502' }
];

export default function CommunitySupport() {
  const { currentUser } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [supportRequests, setSupportRequests] = useState([]);
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [newRequest, setNewRequest] = useState({
    title: '',
    description: '',
    category: '',
    type: '',
    location: '',
    contactMethod: 'platform'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadSupportRequests();
  }, []);

  const loadSupportRequests = () => {
    const requestsRef = collection(db, 'supportRequests');
    const q = query(
      requestsRef,
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const requestData = snapshot.docs.map(doc => ({ 
        id: doc.id, 
        ...doc.data() 
      }));
      setSupportRequests(requestData);
    });

    return unsubscribe;
  };

  const submitSupportRequest = async () => {
    if (!currentUser || !newRequest.title.trim() || !newRequest.type) return;

    setLoading(true);
    setError(null);
    try {
      await addDoc(collection(db, 'supportRequests'), {
        ...newRequest,
        authorId: currentUser.uid,
        authorName: currentUser.displayName || currentUser.email,
        status: 'open',
        responses: [],
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString()
      });

      setNewRequest({
        title: '',
        description: '',
        category: '',
        type: '',
        location: '',
        contactMethod: 'platform'
      });
      setShowRequestForm(false);
      loadSupportRequests(); // Refresh the list
    } catch (error) {
      console.error('Error submitting support request:', error);
      setError('Failed to submit support request. Please check your connection and try again.');
    }
    setLoading(false);
  };

  const respondToRequest = async (requestId, response) => {
    if (!currentUser || !response.trim()) return;

    try {
      const requestRef = doc(db, 'supportRequests', requestId);
      await updateDoc(requestRef, {
        responses: arrayUnion({
          id: Date.now().toString(),
          authorId: currentUser.uid,
          authorName: currentUser.displayName || currentUser.email,
          content: response,
          createdAt: new Date().toISOString()
        }),
        lastActivity: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error responding to request:', error);
    }
  };

  const filteredRequests = useMemo(() => {
    if (!selectedCategory) return supportRequests;
    return supportRequests.filter(request => request.category === selectedCategory.id);
  }, [supportRequests, selectedCategory]);

  if (!selectedCategory) {
    return (
      <div className="community-support">
        <div className="support-header">
          <h2>🤝 Community Support Network</h2>
          <p>Together we're stronger. Find help, offer support, access resources.</p>
        </div>

        <div className="emergency-banner">
          <h3>🚨 Emergency Resources</h3>
          <div className="emergency-contacts">
            <div className="emergency-item">
              <strong>Emergency Services:</strong> 911
            </div>
            <div className="emergency-item">
              <strong>Crisis Text Line:</strong> Text HOME to 741741
            </div>
            <div className="emergency-item">
              <strong>National Suicide Prevention:</strong> 988
            </div>
            <div className="emergency-item">
              <strong>Domestic Violence Hotline:</strong> 1-800-799-7233
            </div>
          </div>
        </div>

        <div className="support-categories">
          <h3>Choose a Support Category</h3>
          <div className="categories-grid">
            {SUPPORT_CATEGORIES.map(category => (
              <div
                key={category.id}
                className="category-card"
                style={{ borderLeft: `4px solid ${category.color}` }}
                onClick={() => setSelectedCategory(category)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setSelectedCategory(category);
                  }
                }}
                aria-label={`Select ${category.title} support category`}
              >
                <div className="category-header">
                  <span className="category-icon">{category.icon}</span>
                  <h4>{category.title}</h4>
                </div>
                <p>{category.description}</p>
                <div className="category-stats">
                  <small>{category.resources.length} resources available</small>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="recent-requests">
          <h3>Recent Community Support Requests</h3>
          <div className="requests-preview">
            {supportRequests.slice(0, 3).map(request => {
              const requestType = SUPPORT_REQUEST_TYPES.find(t => t.id === request.type);
              return (
                <div key={request.id} className="request-preview-card">
                  <div className="request-header">
                    <h4>{request.title}</h4>
                    {requestType && (
                      <span 
                        className="request-type-badge"
                        style={{ backgroundColor: requestType.color }}
                      >
                        {requestType.label}
                      </span>
                    )}
                  </div>
                  <p>{request.description.substring(0, 100)}...</p>
                  <div className="request-meta">
                    <span>{request.location}</span>
                    <span>{new Date(request.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              );
            })}
          </div>
          <button 
            className="view-all-requests-btn"
            onClick={() => setSelectedCategory(SUPPORT_CATEGORIES[0])}
          >
            View All Support Requests
          </button>
        </div>
      </div>
    );
  }



  return (
    <div className="community-support">
      <div className="support-header">
        <button 
          className="back-btn"
          onClick={() => setSelectedCategory(null)}
        >
          ← Back to Categories
        </button>
        <div className="category-info">
          <h2>{selectedCategory.icon} {selectedCategory.title}</h2>
          <p>{selectedCategory.description}</p>
        </div>
      </div>

      <div className="category-resources">
        <h3>📋 Available Resources</h3>
        <div className="resources-grid">
          {selectedCategory.resources.map((resource, index) => (
            <a 
              key={index}
              href={resource.url}
              target="_blank"
              rel="noopener noreferrer"
              className={`resource-link ${resource.urgent ? 'urgent' : ''}`}
            >
              {resource.urgent && <span className="urgent-badge">URGENT</span>}
              {resource.name}
            </a>
          ))}
        </div>
      </div>

      <div className="support-requests-section">
        <div className="requests-header">
          <h3>Community Support Requests ({filteredRequests.length})</h3>
          <button
            className="new-request-btn"
            onClick={() => setShowRequestForm(true)}
            disabled={!currentUser}
            title={!currentUser ? "Please log in to request support" : "Request support from the community"}
          >
            + Request Support
          </button>
        </div>

        {showRequestForm && (
          <div className="request-form">
            <h4>🤝 Request Community Support</h4>

            <div className="form-field">
              <label className="form-label" htmlFor="request-title">
                Request Title *
              </label>
              <input
                id="request-title"
                type="text"
                value={newRequest.title}
                onChange={(e) => setNewRequest({...newRequest, title: e.target.value})}
                placeholder="Brief, clear title for your request"
                className="form-input"
                maxLength={100}
                required
              />
              <div className="character-count">{newRequest.title.length}/100</div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="request-description">
                Description *
              </label>
              <textarea
                id="request-description"
                value={newRequest.description}
                onChange={(e) => setNewRequest({...newRequest, description: e.target.value})}
                placeholder="Describe what kind of support you need or are offering. Be specific about your situation and what would help most."
                className="form-textarea"
                maxLength={500}
                required
              />
              <div className="character-count">{newRequest.description.length}/500</div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="request-type">
                Support Type *
              </label>
              <select
                id="request-type"
                value={newRequest.type}
                onChange={(e) => setNewRequest({...newRequest, type: e.target.value})}
                className="form-select"
                required
              >
                <option value="">Choose the type of support you need</option>
                {SUPPORT_REQUEST_TYPES.map(type => (
                  <option key={type.id} value={type.id}>{type.label}</option>
                ))}
              </select>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="request-location">
                Location (Optional)
              </label>
              <input
                id="request-location"
                type="text"
                value={newRequest.location}
                onChange={(e) => setNewRequest({...newRequest, location: e.target.value})}
                placeholder="City, neighborhood, or general area"
                className="form-input"
                maxLength={50}
              />
              <div className="form-helper-text">
                This helps community members find local support opportunities
              </div>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="contact-method">
                Preferred Contact Method
              </label>
              <select
                id="contact-method"
                value={newRequest.contactMethod}
                onChange={(e) => setNewRequest({...newRequest, contactMethod: e.target.value})}
                className="form-select"
              >
                <option value="platform">Contact through platform</option>
                <option value="email">Email contact</option>
                <option value="phone">Phone contact</option>
              </select>
            </div>

            {error && (
              <div className="form-error-message" style={{ marginBottom: '1rem' }}>
                {error}
              </div>
            )}

            <div className="form-actions">
              <button
                onClick={submitSupportRequest}
                disabled={loading || !newRequest.title.trim() || !newRequest.type}
                className={`submit-btn ${loading ? 'loading' : ''}`}
              >
                {loading ? 'Submitting...' : '📤 Submit Request'}
              </button>
              <button
                onClick={() => {
                  setShowRequestForm(false);
                  setError(null);
                }}
                className="cancel-btn"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="requests-list">
          {filteredRequests.length === 0 ? (
            <div className="no-requests">
              <p>No support requests in this category yet. Be the first to reach out or offer help!</p>
            </div>
          ) : (
            filteredRequests.map(request => (
              <SupportRequestCard 
                key={request.id} 
                request={request} 
                onRespond={respondToRequest}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

function SupportRequestCard({ request, onRespond }) {
  const [showResponses, setShowResponses] = useState(false);
  const [newResponse, setNewResponse] = useState('');

  const requestType = SUPPORT_REQUEST_TYPES.find(t => t.id === request.type);

  const handleRespond = () => {
    if (newResponse.trim()) {
      onRespond(request.id, newResponse);
      setNewResponse('');
    }
  };

  return (
    <div className="support-request-card">
      <div className="request-header">
        <h4>{request.title}</h4>
        {requestType && (
          <span 
            className="request-type-badge"
            style={{ backgroundColor: requestType.color }}
          >
            {requestType.label}
          </span>
        )}
      </div>

      <p className="request-description">{request.description}</p>

      <div className="request-meta">
        <span>📍 {request.location}</span>
        <span>👤 {request.authorName}</span>
        <span>📅 {new Date(request.createdAt).toLocaleDateString()}</span>
        <span>💬 {request.responses?.length || 0} responses</span>
      </div>

      <div className="request-actions">
        <button 
          onClick={() => setShowResponses(!showResponses)}
          className="view-responses-btn"
        >
          {showResponses ? 'Hide' : 'View'} Responses
        </button>
      </div>

      {showResponses && (
        <div className="responses-section">
          <div className="responses-list">
            {request.responses?.map(response => (
              <div key={response.id} className="response-item">
                <div className="response-header">
                  <strong>{response.authorName}</strong>
                  <span>{new Date(response.createdAt).toLocaleDateString()}</span>
                </div>
                <p>{response.content}</p>
              </div>
            ))}
          </div>

          <div className="add-response">
            <textarea
              value={newResponse}
              onChange={(e) => setNewResponse(e.target.value)}
              placeholder="Offer support, resources, or encouragement..."
              className="response-input"
              maxLength={300}
            />
            <button 
              onClick={handleRespond}
              disabled={!newResponse.trim()}
              className="respond-btn"
            >
              Respond
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
